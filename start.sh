#!/bin/bash

echo "========================================"
echo "       QLQ TV - Starting Application"
echo "========================================"
echo

echo "Installing dependencies..."
npm install

echo
echo "Installing server dependencies..."
cd server
npm install
cd ..

echo
echo "Starting QLQ TV..."
echo "Frontend: http://localhost:5173"
echo "Backend API: http://localhost:3001"
echo

# Start frontend in background
npm run dev &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 3

# Start backend in background
npm run server &
BACKEND_PID=$!

echo
echo "QLQ TV is running!"
echo "Frontend PID: $FRONTEND_PID"
echo "Backend PID: $BACKEND_PID"
echo
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo
    echo "Stopping QLQ TV services..."
    kill $FRONTEND_PID 2>/dev/null
    kill $BACKEND_PID 2>/dev/null
    echo "Services stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for background processes
wait
