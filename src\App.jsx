import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import ChannelGrid from './components/ChannelGrid';
import VideoPlayer from './components/VideoPlayer';
import StatsBar from './components/StatsBar';
import CategoryFilter from './components/CategoryFilter';
import FavoriteChannels from './components/FavoriteChannels';
import Sidebar from './components/Sidebar';
import channelsData from './data/channels.json';
import streamService from './services/streamService';

function App() {
  const [channels, setChannels] = useState([]);
  const [filteredChannels, setFilteredChannels] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [selectedStream, setSelectedStream] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState('all');
  const [showFavorites, setShowFavorites] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  useEffect(() => {
    initializeChannels();
  }, []);

  useEffect(() => {
    filterChannelsByCategory();
  }, [channels, activeCategory]);

  const initializeChannels = async () => {
    try {
      setIsLoading(true);

      // دمج قنوات beIN Sports و OSN
      const allChannels = [
        ...channelsData.beinSports,
        ...channelsData.osn
      ];

      setChannels(allChannels);
      setFilteredChannels(allChannels);
    } catch (error) {
      console.error('خطأ في تحميل القنوات:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterChannelsByCategory = () => {
    let filtered = channels;

    if (activeCategory !== 'all') {
      if (activeCategory === 'bein-sports') {
        filtered = channels.filter(channel => channel.id.includes('bein'));
      } else if (activeCategory === 'osn') {
        filtered = channels.filter(channel => channel.id.includes('osn'));
      } else {
        filtered = channels.filter(channel => channel.category === activeCategory);
      }
    }

    setFilteredChannels(filtered);
  };

  const handleSearch = async (query) => {
    setSearchQuery(query);

    if (query.trim()) {
      try {
        // البحث عن قنوات جديدة
        const newChannels = await streamService.searchNewChannels(query);
        if (newChannels.length > 0) {
          setChannels(prev => [...prev, ...newChannels]);
        }
      } catch (error) {
        console.error('خطأ في البحث:', error);
      }
    }
  };

  const handleChannelSelect = (channel, stream) => {
    setSelectedChannel(channel);
    setSelectedStream(stream);
  };

  const handleClosePlayer = () => {
    setSelectedChannel(null);
    setSelectedStream(null);
  };

  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
    setShowFavorites(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2 arabic-text">QLQ TV</h2>
          <p className="text-gray-400 arabic-text">جاري تحميل القنوات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-900">
      <Header
        onSearch={handleSearch}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />

      <StatsBar
        totalChannels={channels.length}
        activeStreams={Math.floor(Math.random() * 10) + 5}
      />

      <CategoryFilter
        onCategoryChange={handleCategoryChange}
        activeCategory={activeCategory}
      />

      <main className="container py-8">
        <div className="flex gap-8">
          {/* المحتوى الرئيسي */}
          <div className="flex-1">
            {/* أزرار التبديل */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowFavorites(false)}
                  className={`btn ${!showFavorites ? 'btn-primary' : 'btn-secondary'} arabic-text`}
                >
                  جميع القنوات
                </button>
                <button
                  onClick={() => setShowFavorites(true)}
                  className={`btn ${showFavorites ? 'btn-primary' : 'btn-secondary'} arabic-text`}
                >
                  المفضلة
                </button>
              </div>

              {/* زر إخفاء/إظهار الشريط الجانبي */}
              <button
                onClick={() => setShowSidebar(!showSidebar)}
                className="btn btn-secondary lg:hidden arabic-text"
              >
                {showSidebar ? 'إخفاء الشريط' : 'إظهار الشريط'}
              </button>
            </div>

            {showFavorites ? (
              <FavoriteChannels
                channels={channels}
                onChannelSelect={handleChannelSelect}
              />
            ) : (
              <ChannelGrid
                channels={filteredChannels}
                onChannelSelect={handleChannelSelect}
                searchQuery={searchQuery}
              />
            )}
          </div>

          {/* الشريط الجانبي */}
          {showSidebar && (
            <div className="hidden lg:block">
              <Sidebar onChannelSelect={handleChannelSelect} />
            </div>
          )}
        </div>

        {/* الشريط الجانبي للهواتف */}
        {showSidebar && (
          <div className="lg:hidden mt-8">
            <Sidebar onChannelSelect={handleChannelSelect} />
          </div>
        )}
      </main>

      {/* مشغل الفيديو */}
      {selectedChannel && selectedStream && (
        <VideoPlayer
          channel={selectedChannel}
          stream={selectedStream}
          onClose={handleClosePlayer}
        />
      )}

      {/* تذييل الصفحة */}
      <footer className="bg-dark-800 py-8 mt-16" style={{ borderTop: '1px solid rgba(59, 130, 246, 0.2)' }}>
        <div className="container text-center">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white arabic-text">QLQ TV</h3>
          </div>

          <p className="text-gray-400 mb-4 arabic-text">
            منصة البث المباشر للقنوات المشفرة - beIN Sports & OSN
          </p>

          <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
            <span className="arabic-text">© 2024 QLQ TV</span>
            <span>•</span>
            <span className="arabic-text">جميع الحقوق محفوظة</span>
            <span>•</span>
            <span className="arabic-text">البث المباشر</span>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
