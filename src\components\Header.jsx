import React, { useState } from 'react';

const Header = ({ onSearch, searchQuery, setSearchQuery }) => {
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  return (
    <header className="glass-dark border-b border-primary-500/20 sticky top-0 z-40">
      <div className="container py-6">
        {/* الصف الأول - الشعار والبحث والأدوات */}
        <div className="flex items-center justify-between gap-6 mb-6">
          {/* شعار الموقع المحسن */}
          <div className="flex items-center gap-4">
            <div className="relative group">
              <div className="w-20 h-20 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-800 rounded-2xl flex items-center justify-center shadow-2xl transform group-hover:scale-105 transition-all duration-300">
                <div className="relative">
                  <svg
                    className="w-10 h-10 text-white drop-shadow-lg"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14zM8 15c0-1.66 1.34-3 3-3s3 1.34 3 3-1.34 3-3 3-3-1.34-3-3zm8-5c0-.55-.45-1-1-1s-1 .45-1 1 .45 1 1 1 1-.45 1-1zm-2-3c0-.55-.45-1-1-1s-1 .45-1 1 .45 1 1 1 1-.45 1-1zm-4 0c0-.55-.45-1-1-1s-1 .45-1 1 .45 1 1 1 1-.45 1-1zm-2 3c0-.55-.45-1-1-1s-1 .45-1 1 .45 1 1 1 1-.45 1-1z"/>
                  </svg>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse shadow-lg">
                    <div className="w-2 h-2 bg-white rounded-full absolute top-1 left-1"></div>
                  </div>
                </div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-br from-primary-400 to-primary-700 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </div>
            <div>
              <h1 className="text-4xl font-black text-white arabic-text bg-gradient-to-r from-white to-primary-200 bg-clip-text text-transparent">
                QLQ TV
              </h1>
              <p className="text-primary-300 text-base font-medium arabic-text">
                🔴 البث المباشر للقنوات المشفرة
              </p>
              <div className="flex items-center gap-2 mt-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-400 font-medium">متاح الآن</span>
              </div>
            </div>
          </div>

          {/* شريط البحث المحسن */}
          <div className="flex-1 max-w-lg">
            <form onSubmit={handleSearchSubmit} className="relative">
              <div className={`relative transition-all duration-300 ${isSearchFocused ? 'scale-105' : ''}`}>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-700 rounded-2xl opacity-20 blur-sm"></div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  placeholder="🔍 ابحث عن القنوات، المباريات، الأخبار..."
                  className="relative w-full px-6 py-4 pr-14 input text-white placeholder-gray-300 text-base font-medium arabic-text"
                />
                <button
                  type="submit"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 text-primary-400 hover:text-white hover:bg-primary-500 rounded-xl transition-all duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </form>
          </div>

          {/* أدوات التحكم المحسنة */}
          <div className="flex items-center gap-3">
            <div className="hidden lg:flex items-center gap-3 px-4 py-2 glass rounded-xl">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg"></div>
              <span className="text-sm font-medium text-white arabic-text">مباشر</span>
              <span className="text-xs text-gray-300">1.2K مشاهد</span>
            </div>

            <div className="flex items-center gap-2">
              <button className="p-3 glass hover:bg-white/20 rounded-xl transition-all duration-200 group">
                <svg className="w-6 h-6 text-gray-300 group-hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </button>

              <button className="p-3 glass hover:bg-white/20 rounded-xl transition-all duration-200 group">
                <svg className="w-6 h-6 text-gray-300 group-hover:text-red-400 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>

              <button className="p-3 glass hover:bg-white/20 rounded-xl transition-all duration-200 group">
                <svg className="w-6 h-6 text-gray-300 group-hover:text-white transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* شريط التنقل المحسن */}
        <nav className="border-t border-white/10 pt-4">
          <div className="flex flex-wrap items-center justify-center gap-4">
            {[
              { name: 'جميع القنوات', icon: '📺', active: true },
              { name: 'beIN Sports', icon: '⚽', active: false },
              { name: 'OSN', icon: '🎬', active: false },
              { name: 'الرياضة', icon: '🏆', active: false },
              { name: 'الترفيه', icon: '🎭', active: false },
              { name: 'الأفلام', icon: '🍿', active: false },
              { name: 'الأخبار', icon: '📰', active: false }
            ].map((item, index) => (
              <button
                key={index}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-xl font-medium text-sm transition-all duration-200 arabic-text
                  ${item.active
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg transform scale-105'
                    : 'glass text-gray-300 hover:text-white hover:bg-white/20 hover:scale-105'
                  }
                `}
              >
                <span className="text-lg">{item.icon}</span>
                <span>{item.name}</span>
                {item.active && (
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                )}
              </button>
            ))}
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
