const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// مصادر البحث عن القنوات
const streamSources = [
  'https://iptv-org.github.io/api/channels.json',
  'https://raw.githubusercontent.com/iptv-org/iptv/master/channels.json'
];

// قاعدة بيانات مؤقتة للمصادر المحفوظة
let cachedStreams = new Map();

// API للبحث عن مصادر البث
app.get('/api/streams/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { channelName } = req.query;

    // التحقق من الكاش أولاً
    if (cachedStreams.has(channelId)) {
      const cached = cachedStreams.get(channelId);
      if (Date.now() - cached.timestamp < 300000) { // 5 دقائق
        return res.json(cached.streams);
      }
    }

    // البحث عن مصادر جديدة
    const streams = await searchForStreams(channelName || channelId);
    
    // حفظ في الكاش
    cachedStreams.set(channelId, {
      streams,
      timestamp: Date.now()
    });

    res.json(streams);
  } catch (error) {
    console.error('خطأ في البحث عن المصادر:', error);
    res.status(500).json({ error: 'فشل في البحث عن المصادر' });
  }
});

// API للبحث عن قنوات جديدة
app.get('/api/search', async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q) {
      return res.status(400).json({ error: 'مطلوب كلمة البحث' });
    }

    const channels = await searchChannels(q);
    res.json(channels);
  } catch (error) {
    console.error('خطأ في البحث عن القنوات:', error);
    res.status(500).json({ error: 'فشل في البحث عن القنوات' });
  }
});

// API لفحص صحة المصدر
app.post('/api/validate-stream', async (req, res) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'مطلوب رابط المصدر' });
    }

    const isValid = await validateStream(url);
    res.json({ valid: isValid });
  } catch (error) {
    console.error('خطأ في فحص المصدر:', error);
    res.status(500).json({ error: 'فشل في فحص المصدر' });
  }
});

// دالة البحث عن مصادر البث
async function searchForStreams(channelName) {
  const streams = [];
  
  try {
    // محاكاة البحث في مصادر مختلفة
    const mockStreams = [
      {
        url: `https://example-stream-${Date.now()}.m3u8`,
        quality: 'HD',
        source: 'IPTV-Org',
        type: 'hls',
        status: 'active'
      },
      {
        url: `https://backup-stream-${Date.now()}.m3u8`,
        quality: 'SD',
        source: 'Backup Server',
        type: 'hls',
        status: 'active'
      }
    ];

    // في التطبيق الحقيقي، يمكن البحث في مصادر حقيقية
    for (const source of streamSources) {
      try {
        // محاكاة طلب API
        // const response = await axios.get(source);
        // معالجة البيانات المستلمة
        streams.push(...mockStreams);
      } catch (error) {
        console.error(`خطأ في المصدر ${source}:`, error.message);
      }
    }

    return streams;
  } catch (error) {
    console.error('خطأ في البحث عن المصادر:', error);
    return [];
  }
}

// دالة البحث عن قنوات جديدة
async function searchChannels(query) {
  try {
    // محاكاة البحث عن قنوات
    const mockChannels = [
      {
        id: `search_${Date.now()}_1`,
        name: `${query} HD`,
        logo: 'https://via.placeholder.com/150',
        category: 'search',
        language: 'ar',
        description: `قناة ${query} - البث المباشر`,
        streams: []
      },
      {
        id: `search_${Date.now()}_2`,
        name: `${query} Sports`,
        logo: 'https://via.placeholder.com/150',
        category: 'sports',
        language: 'ar',
        description: `قناة ${query} الرياضية - البث المباشر`,
        streams: []
      }
    ];

    return mockChannels;
  } catch (error) {
    console.error('خطأ في البحث عن القنوات:', error);
    return [];
  }
}

// دالة فحص صحة المصدر
async function validateStream(url) {
  try {
    // محاكاة فحص المصدر
    // في التطبيق الحقيقي، يمكن إرسال طلب HEAD للتحقق
    const response = await axios.head(url, {
      timeout: 5000,
      validateStatus: (status) => status < 400
    });
    
    return response.status === 200;
  } catch (error) {
    // محاكاة نتيجة عشوائية للاختبار
    return Math.random() > 0.3;
  }
}

// API للحصول على إحصائيات
app.get('/api/stats', (req, res) => {
  res.json({
    totalChannels: 16,
    activeStreams: cachedStreams.size,
    uptime: process.uptime(),
    lastUpdate: new Date().toISOString()
  });
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('خطأ في الخادم:', error);
  res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

// معالج الطرق غير الموجودة
app.use('*', (req, res) => {
  res.status(404).json({ error: 'الطريق غير موجود' });
});

// تشغيل الخادم
app.listen(PORT, () => {
  console.log(`🚀 خادم QLQ TV يعمل على المنفذ ${PORT}`);
  console.log(`📺 API متاح على: http://localhost:${PORT}/api`);
});

module.exports = app;
