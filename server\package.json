{"name": "qlq-tv-server", "version": "1.0.0", "description": "QLQ TV Backend Server for Stream Discovery", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["iptv", "streaming", "bein-sports", "osn", "live-tv"], "author": "QLQ TV", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}}