# QLQ TV - منصة البث المباشر للقنوات المشفرة

<div align="center">
  <img src="https://img.shields.io/badge/React-19-blue?style=for-the-badge&logo=react" alt="React">
  <img src="https://img.shields.io/badge/Vite-5-purple?style=for-the-badge&logo=vite" alt="Vite">
  <img src="https://img.shields.io/badge/Node.js-18-green?style=for-the-badge&logo=node.js" alt="Node.js">
  <img src="https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge" alt="License">
</div>

## 📺 نظرة عامة

QLQ TV هو موقع احترافي لتشغيل القنوات المشفرة مثل beIN Sports و OSN مع البحث التلقائي عن مصادر مفتوحة للبث المباشر.

## ✨ المميزات

- 🎯 **بث مباشر عالي الجودة** - دعم HD و FHD
- 🔍 **البحث التلقائي** - العثور على مصادر البث تلقائياً
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🎨 **واجهة احترافية** - تصميم عصري باللغة العربية
- ⚡ **أداء سريع** - تحميل فوري للقنوات
- 🔄 **تحديث تلقائي** - البحث عن مصادر جديدة باستمرار
- ❤️ **نظام المفضلة** - حفظ القنوات المفضلة
- 📰 **الأخبار الرياضية** - آخر الأخبار والمباريات
- 🎮 **واجهة تفاعلية** - تجربة مستخدم محسنة

## 🏗️ التقنيات المستخدمة

### Frontend
- **React 19** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **CSS مخصص** - تصميم احترافي
- **HLS.js** - مشغل البث المباشر
- **Video.js** - مشغل الفيديو المتقدم

### Backend
- **Express.js** - خادم API
- **Axios** - طلبات HTTP
- **CORS** - دعم الطلبات المتقاطعة

## 📋 القنوات المدعومة

### beIN Sports
- beIN Sports 1-7 HD
- beIN Sports Premium 1-3
- جميع القنوات الرياضية

### OSN
- OSN Sports 1-4 HD
- OSN Ya Hala HD
- OSN Ya Hala Cinema
- قنوات الترفيه والأفلام

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn

### التشغيل السريع (Windows)
```bash
# تشغيل تلقائي
start.bat
```

### التشغيل السريع (Linux/Mac)
```bash
# جعل الملف قابل للتنفيذ
chmod +x start.sh

# تشغيل تلقائي
./start.sh
```

### التشغيل اليدوي
```bash
# تثبيت التبعيات
npm run setup

# تشغيل التطوير
npm run full-dev

# تشغيل الإنتاج
npm run full-prod
```

### التشغيل باستخدام Docker
```bash
# بناء وتشغيل
npm run docker:compose

# إيقاف
npm run docker:down
```

## 🌐 الوصول للموقع

- **الواجهة الأمامية**: `http://localhost:5173`
- **خادم API**: `http://localhost:3001`

## 📁 هيكل المشروع

```
qlq-tv/
├── src/
│   ├── components/          # مكونات React
│   │   ├── Header.jsx      # رأس الصفحة
│   │   ├── ChannelGrid.jsx # شبكة القنوات
│   │   ├── VideoPlayer.jsx # مشغل الفيديو
│   │   ├── Sidebar.jsx     # الشريط الجانبي
│   │   ├── StatsBar.jsx    # شريط الإحصائيات
│   │   └── ...             # مكونات أخرى
│   ├── data/               # بيانات القنوات
│   │   └── channels.json   # قائمة القنوات
│   ├── services/           # خدمات API
│   │   └── streamService.js # خدمة البحث عن المصادر
│   └── index.css           # ملفات التصميم
├── server/                 # خادم API
│   ├── index.js           # الخادم الرئيسي
│   └── package.json       # تبعيات الخادم
├── public/                # الملفات العامة
├── docker-compose.yml     # تكوين Docker
├── nginx.conf             # تكوين Nginx
└── README.md              # هذا الملف
```

## 🎮 كيفية الاستخدام

1. **تصفح القنوات** - استعرض القنوات المتاحة في الصفحة الرئيسية
2. **البحث** - استخدم شريط البحث للعثور على قنوات محددة
3. **التشغيل** - انقر على أي قناة لبدء البث المباشر
4. **المفضلة** - أضف قنواتك المفضلة بالنقر على أيقونة القلب
5. **الأخبار** - تابع آخر الأخبار الرياضية في الشريط الجانبي
6. **المباريات** - شاهد المباريات القادمة ومواعيدها

## 🔍 مصادر البث

الموقع يبحث تلقائياً في:
- IPTV-Org Database
- قوائم M3U العامة
- مصادر البث المفتوحة
- خوادم البث المجانية

## 📱 الدعم المتجاوب

- 📱 **الهواتف الذكية** - تجربة محسنة للشاشات الصغيرة
- 📟 **الأجهزة اللوحية** - واجهة متكيفة للشاشات المتوسطة
- 💻 **أجهزة الكمبيوتر** - تجربة كاملة للشاشات الكبيرة
- 📺 **أجهزة التلفزيون الذكية** - دعم التشغيل على التلفزيون

## 🌐 النشر

### Vercel
```bash
# تثبيت Vercel CLI
npm i -g vercel

# نشر
vercel --prod
```

### Netlify
```bash
# تثبيت Netlify CLI
npm i -g netlify-cli

# نشر
netlify deploy --prod
```

## 🔧 المتغيرات البيئية

انسخ ملف `.env` وقم بتخصيص القيم حسب احتياجاتك:

```env
VITE_APP_NAME=QLQ TV
VITE_API_BASE_URL=http://localhost:3001/api
VITE_ENABLE_FAVORITES=true
VITE_DEBUG_MODE=true
```

## 📊 الأداء والتحسين

- ⚡ **تحميل سريع** - أقل من 3 ثوانٍ
- 🗜️ **ضغط Gzip** - تقليل حجم الملفات بنسبة 70%
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🔄 **تحديث تلقائي** - Hot Module Replacement
- 💾 **تخزين مؤقت ذكي** - تحسين استهلاك البيانات

## 🛡️ الأمان

- 🔒 **HTTPS** - تشفير جميع الاتصالات
- 🚫 **حماية XSS** - منع الهجمات الضارة
- 🔐 **CSP Headers** - سياسة أمان المحتوى
- 🛡️ **Rate Limiting** - حماية من الهجمات

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. إجراء التغييرات وإضافة الاختبارات
4. تشغيل `npm run lint:fix` لإصلاح الأخطاء
5. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## ⚠️ إخلاء المسؤولية

هذا البرنامج مخصص للاستخدام التعليمي والشخصي فقط. المستخدمون مسؤولون عن ضمان الامتثال لجميع القوانين واللوائح المعمول بها فيما يتعلق ببث المحتوى في ولايتهم القضائية.

---

**QLQ TV** - منصة البث المباشر الاحترافية للقنوات المشفرة 📺✨
