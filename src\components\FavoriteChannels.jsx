import React, { useState, useEffect } from 'react';

const FavoriteChannels = ({ channels, onChannelSelect }) => {
  const [favorites, setFavorites] = useState([]);

  useEffect(() => {
    // تحميل المفضلة من localStorage
    const savedFavorites = localStorage.getItem('qlq-tv-favorites');
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites));
    }
  }, []);

  const toggleFavorite = (channelId) => {
    const newFavorites = favorites.includes(channelId)
      ? favorites.filter(id => id !== channelId)
      : [...favorites, channelId];
    
    setFavorites(newFavorites);
    localStorage.setItem('qlq-tv-favorites', JSON.stringify(newFavorites));
  };

  const favoriteChannels = channels.filter(channel => favorites.includes(channel.id));

  if (favoriteChannels.length === 0) {
    return (
      <div className="bg-dark-800 rounded-xl p-6 text-center">
        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <h3 className="text-lg font-semibold text-gray-300 mb-2 arabic-text">
          لا توجد قنوات مفضلة
        </h3>
        <p className="text-gray-400 arabic-text">
          أضف قنواتك المفضلة لسهولة الوصول إليها
        </p>
      </div>
    );
  }

  return (
    <div className="bg-dark-800 rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-white arabic-text">
          القنوات المفضلة
        </h3>
        <span className="text-sm text-gray-400 arabic-text">
          {favoriteChannels.length} قناة
        </span>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {favoriteChannels.map((channel) => (
          <div
            key={channel.id}
            className="relative group cursor-pointer"
            onClick={() => onChannelSelect(channel)}
          >
            <div className="aspect-square bg-dark-700 rounded-lg overflow-hidden border border-dark-600 hover:border-primary-500 transition-all duration-200">
              <img
                src={channel.logo}
                alt={channel.name}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                onError={(e) => {
                  e.target.src = 'https://via.placeholder.com/100x100/1e293b/3b82f6?text=' + encodeURIComponent(channel.name.substring(0, 2));
                }}
              />
              
              {/* تراكب التشغيل */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                <div className="transform scale-0 group-hover:scale-100 transition-transform duration-300">
                  <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>

              {/* زر إزالة من المفضلة */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(channel.id);
                }}
                className="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
              >
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
              </button>

              {/* مؤشر البث المباشر */}
              <div className="absolute bottom-1 left-1 bg-red-500 text-white text-xs px-1 py-0.5 rounded arabic-text">
                مباشر
              </div>
            </div>

            <p className="text-sm text-gray-300 mt-2 text-center arabic-text truncate">
              {channel.name}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

// Hook لإدارة المفضلة
export const useFavorites = () => {
  const [favorites, setFavorites] = useState([]);

  useEffect(() => {
    const savedFavorites = localStorage.getItem('qlq-tv-favorites');
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites));
    }
  }, []);

  const toggleFavorite = (channelId) => {
    const newFavorites = favorites.includes(channelId)
      ? favorites.filter(id => id !== channelId)
      : [...favorites, channelId];
    
    setFavorites(newFavorites);
    localStorage.setItem('qlq-tv-favorites', JSON.stringify(newFavorites));
  };

  const isFavorite = (channelId) => favorites.includes(channelId);

  return { favorites, toggleFavorite, isFavorite };
};

export default FavoriteChannels;
