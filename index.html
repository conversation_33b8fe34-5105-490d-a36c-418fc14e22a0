<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="QLQ TV - منصة البث المباشر للقنوات المشفرة beIN Sports و OSN" />
    <meta name="keywords" content="QLQ TV, بث مباشر, قنوات مشفرة, beIN Sports, OSN, رياضة, أفلام" />
    <meta name="author" content="QLQ TV" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="QLQ TV - البث المباشر للقنوات المشفرة" />
    <meta property="og:description" content="شاهد قنوات beIN Sports و OSN مباشرة مع أفضل جودة" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="QLQ TV - البث المباشر للقنوات المشفرة" />
    <meta name="twitter:description" content="شاهد قنوات beIN Sports و OSN مباشرة مع أفضل جودة" />

    <title>QLQ TV - البث المباشر للقنوات المشفرة</title>

    <!-- Preload important fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">

    <style>
      /* Critical CSS for loading screen */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-spinner {
        border: 3px solid #1e293b;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <div style="text-align: center; color: white; font-family: 'Cairo', sans-serif;">
        <div class="loading-spinner" style="margin: 0 auto 1rem;"></div>
        <h2 style="margin: 0 0 0.5rem; font-size: 1.5rem; font-weight: bold;">QLQ TV</h2>
        <p style="margin: 0; color: #9ca3af;">جاري التحميل...</p>
      </div>
    </div>

    <div id="root"></div>

    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>

    <script type="module" src="/src/main.jsx"></script>

    <script>
      // Hide loading screen when page is loaded
      window.addEventListener('load', function() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.style.opacity = '0';
          loadingScreen.style.transition = 'opacity 0.5s ease';
          setTimeout(() => {
            loadingScreen.style.display = 'none';
          }, 500);
        }
      });
    </script>
  </body>
</html>
