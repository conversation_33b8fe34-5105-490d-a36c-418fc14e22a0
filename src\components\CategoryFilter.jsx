import React, { useState } from 'react';

const CategoryFilter = ({ onCategoryChange, activeCategory }) => {
  const categories = [
    {
      id: 'all',
      name: 'جميع القنوات',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      ),
      count: 16
    },
    {
      id: 'bein-sports',
      name: 'beIN Sports',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      ),
      count: 10
    },
    {
      id: 'osn',
      name: 'OSN',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z"/>
        </svg>
      ),
      count: 6
    },
    {
      id: 'sports',
      name: 'الرياضة',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      ),
      count: 14
    },
    {
      id: 'entertainment',
      name: 'الترفيه',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
        </svg>
      ),
      count: 1
    },
    {
      id: 'movies',
      name: 'الأفلام',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z"/>
        </svg>
      ),
      count: 1
    }
  ];

  const handleCategoryClick = (categoryId) => {
    onCategoryChange(categoryId);
  };

  return (
    <div className="bg-dark-900 py-4">
      <div className="container">
        <div className="flex items-center justify-center">
          <div className="flex flex-wrap items-center justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleCategoryClick(category.id)}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 arabic-text
                  ${activeCategory === category.id 
                    ? 'bg-primary-600 text-white shadow-lg' 
                    : 'bg-dark-800 text-gray-300 hover:bg-dark-700 hover:text-white'
                  }
                `}
              >
                {category.icon}
                <span className="font-medium">{category.name}</span>
                <span className={`
                  text-xs px-2 py-1 rounded-full
                  ${activeCategory === category.id 
                    ? 'bg-white bg-opacity-20 text-white' 
                    : 'bg-primary-600 text-white'
                  }
                `}>
                  {category.count}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* فلاتر إضافية */}
        <div className="flex items-center justify-center mt-4 gap-4">
          <div className="flex items-center gap-2">
            <label className="text-gray-300 text-sm arabic-text">الجودة:</label>
            <select className="input text-sm py-1 px-2">
              <option value="all">جميع الجودات</option>
              <option value="fhd">FHD</option>
              <option value="hd">HD</option>
              <option value="sd">SD</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-gray-300 text-sm arabic-text">اللغة:</label>
            <select className="input text-sm py-1 px-2">
              <option value="all">جميع اللغات</option>
              <option value="ar">العربية</option>
              <option value="en">الإنجليزية</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-gray-300 text-sm arabic-text">الترتيب:</label>
            <select className="input text-sm py-1 px-2">
              <option value="name">الاسم</option>
              <option value="popularity">الشعبية</option>
              <option value="recent">الأحدث</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryFilter;
