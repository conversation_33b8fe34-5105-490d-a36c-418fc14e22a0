{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "@eslint/js/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error", "object-shorthand": "error", "prefer-template": "error"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["dist/", "node_modules/", "server/node_modules/"]}