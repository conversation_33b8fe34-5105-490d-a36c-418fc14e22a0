import React, { useState, useEffect } from 'react';

const StatsBar = ({ totalChannels, activeStreams }) => {
  const [stats, setStats] = useState({
    totalChannels: 0,
    activeStreams: 0,
    onlineUsers: 0,
    uptime: '0:00:00'
  });

  useEffect(() => {
    // محاكاة تحديث الإحصائيات
    const updateStats = () => {
      setStats({
        totalChannels: totalChannels || 16,
        activeStreams: activeStreams || Math.floor(Math.random() * 10) + 5,
        onlineUsers: Math.floor(Math.random() * 1000) + 500,
        uptime: formatUptime(Date.now())
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 30000); // تحديث كل 30 ثانية

    return () => clearInterval(interval);
  }, [totalChannels, activeStreams]);

  const formatUptime = (timestamp) => {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-dark-800 border-t border-b" style={{ borderColor: 'rgba(59, 130, 246, 0.2)' }}>
      <div className="container py-4">
        <div className="flex items-center justify-center gap-8 text-sm">
          {/* إجمالي القنوات */}
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary-600 rounded-full animate-pulse"></div>
            <span className="text-gray-300 arabic-text">القنوات:</span>
            <span className="text-white font-semibold">{stats.totalChannels}</span>
          </div>

          {/* المصادر النشطة */}
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-gray-300 arabic-text">مصادر نشطة:</span>
            <span className="text-white font-semibold">{stats.activeStreams}</span>
          </div>

          {/* المستخدمون المتصلون */}
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
            <span className="text-gray-300 arabic-text">متصل الآن:</span>
            <span className="text-white font-semibold">{stats.onlineUsers.toLocaleString()}</span>
          </div>

          {/* وقت التشغيل */}
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-primary-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span className="text-gray-300 arabic-text">وقت التشغيل:</span>
            <span className="text-white font-semibold">{stats.uptime}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsBar;
