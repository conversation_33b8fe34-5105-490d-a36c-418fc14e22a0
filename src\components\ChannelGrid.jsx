import React, { useState, useEffect } from 'react';
import streamService from '../services/streamService';
import { useFavorites } from './FavoriteChannels';

const ChannelGrid = ({ channels, onChannelSelect, searchQuery }) => {
  const [filteredChannels, setFilteredChannels] = useState([]);
  const [loadingChannels, setLoadingChannels] = useState(new Set());
  const { toggleFavorite, isFavorite } = useFavorites();

  useEffect(() => {
    filterChannels();
  }, [channels, searchQuery]);

  const filterChannels = () => {
    if (!searchQuery) {
      setFilteredChannels(channels);
      return;
    }

    const filtered = channels.filter(channel =>
      channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      channel.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredChannels(filtered);
  };

  const handleChannelClick = async (channel) => {
    if (loadingChannels.has(channel.id)) return;

    setLoadingChannels(prev => new Set([...prev, channel.id]));

    try {
      // البحث عن مصادر البث للقناة
      const updatedChannel = await streamService.updateChannelStreams(channel);

      if (updatedChannel.streams && updatedChannel.streams.length > 0) {
        const bestStream = streamService.getBestStream(updatedChannel.streams);
        onChannelSelect(updatedChannel, bestStream);
      } else {
        // إظهار رسالة عدم توفر مصادر
        alert('عذراً، لا توجد مصادر متاحة لهذه القناة حالياً');
      }
    } catch (error) {
      console.error('خطأ في تحميل القناة:', error);
      alert('حدث خطأ في تحميل القناة');
    } finally {
      setLoadingChannels(prev => {
        const newSet = new Set(prev);
        newSet.delete(channel.id);
        return newSet;
      });
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'sports':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        );
      case 'entertainment':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
          </svg>
        );
      case 'movies':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z"/>
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        );
    }
  };

  const getStatusIndicator = (channel) => {
    if (loadingChannels.has(channel.id)) {
      return (
        <div className="absolute top-2 left-2 w-3 h-3">
          <div className="loading-spinner w-3 h-3 border-2"></div>
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
    );
  };

  if (filteredChannels.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <svg className="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.175-5.5-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
        <h3 className="text-xl font-semibold text-gray-300 mb-2 arabic-text">
          لا توجد قنوات
        </h3>
        <p className="text-gray-400 arabic-text">
          {searchQuery ? 'لم يتم العثور على قنوات تطابق البحث' : 'لا توجد قنوات متاحة حالياً'}
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white mb-2 arabic-text">
          القنوات المتاحة
        </h2>
        <p className="text-gray-400 arabic-text">
          {filteredChannels.length} قناة متاحة
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredChannels.map((channel, index) => (
          <div
            key={channel.id}
            onClick={() => handleChannelClick(channel)}
            className="channel-card group"
          >
            {/* مؤشر الحالة */}
            {getStatusIndicator(channel)}

            {/* صورة القناة المحسنة */}
            <div className="aspect-video bg-gradient-to-br from-dark-700 to-dark-800 rounded-t-2xl overflow-hidden relative">
              <img
                src={channel.logo}
                alt={channel.name}
                className="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
                onError={(e) => {
                  e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(channel.name)}&background=2563eb&color=ffffff&size=300&format=png`;
                }}
              />

              {/* تراكب متدرج */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/20"></div>

              {/* تراكب التشغيل المحسن */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
                <div className="transform scale-0 group-hover:scale-100 transition-all duration-300 rotate-0 group-hover:rotate-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center shadow-2xl">
                    <svg className="w-10 h-10 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
              </div>

              {/* رقم القناة */}
              <div className="absolute top-3 right-3 w-8 h-8 bg-black/60 backdrop-blur-sm text-white rounded-xl flex items-center justify-center text-sm font-bold">
                {index + 1}
              </div>

              {/* شارة الفئة المحسنة */}
              <div className="absolute top-3 left-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white px-3 py-1 rounded-xl text-xs flex items-center gap-2 shadow-lg">
                {getCategoryIcon(channel.category)}
                <span className="arabic-text font-medium">
                  {channel.category === 'sports' ? 'رياضة' :
                   channel.category === 'entertainment' ? 'ترفيه' :
                   channel.category === 'movies' ? 'أفلام' : 'عام'}
                </span>
              </div>

              {/* زر المفضلة المحسن */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(channel.id);
                }}
                className={`absolute bottom-3 right-3 w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 shadow-lg ${
                  isFavorite(channel.id)
                    ? 'bg-red-500 text-white scale-110'
                    : 'bg-black/60 backdrop-blur-sm text-gray-300 hover:bg-red-500 hover:text-white hover:scale-110'
                }`}
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
              </button>

              {/* مؤشر البث المباشر المحسن */}
              <div className="absolute bottom-3 left-3 bg-red-500 text-white text-xs px-3 py-1 rounded-xl flex items-center gap-2 shadow-lg">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span className="arabic-text font-medium">🔴 مباشر</span>
              </div>
            </div>

            {/* معلومات القناة المحسنة */}
            <div className="p-6 bg-gradient-to-br from-dark-800 to-dark-900">
              <div className="flex items-start justify-between mb-3">
                <h3 className="text-lg font-bold text-white arabic-text group-hover:text-primary-400 transition-colors duration-200">
                  {channel.name}
                </h3>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-400 font-medium">HD</span>
                </div>
              </div>

              <p className="text-gray-400 text-sm mb-4 arabic-text line-clamp-2 leading-relaxed">
                {channel.description}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <span className="text-xs text-gray-500 arabic-text">
                      {channel.language === 'ar' ? 'عربي' : 'إنجليزي'}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span className="text-xs text-green-400 arabic-text font-medium">
                      متاح
                    </span>
                  </div>
                </div>

                <button className="btn btn-primary text-xs opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-y-2 group-hover:translate-y-0">
                  ▶️ مشاهدة
                </button>
              </div>

              {/* شريط التحميل المحسن */}
              {loadingChannels.has(channel.id) && (
                <div className="mt-4 flex items-center gap-3">
                  <div className="flex-1 bg-dark-600 rounded-full h-2 overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-primary-500 to-primary-600 rounded-full animate-pulse"></div>
                  </div>
                  <span className="text-xs text-primary-400 arabic-text">جاري التحميل...</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChannelGrid;
