{"version": 2, "name": "qlq-tv", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}, {"src": "server/index.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/server/index.js"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_APP_NAME": "QLQ TV", "VITE_APP_VERSION": "1.0.0", "VITE_API_BASE_URL": "https://qlq-tv.vercel.app/api"}, "functions": {"server/index.js": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}