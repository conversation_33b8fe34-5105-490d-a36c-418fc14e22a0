import React, { useState, useEffect } from 'react';

const SportsNews = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // تحميل الأخبار الرياضية الحديثة
    const getCurrentNews = () => {
      const now = new Date();
      const today = now.toISOString().split('T')[0];

      return [
        {
          id: 1,
          title: 'مانشستر سيتي يواجه ريال مدريد في دوري الأبطال',
          summary: 'مواجهة نارية متوقعة في ربع نهائي دوري أبطال أوروبا اليوم',
          image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=300&h=200&fit=crop',
          time: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // منذ ساعتين
          category: 'كرة القدم',
          isBreaking: true
        },
        {
          id: 2,
          title: 'محمد صلاح يقترب من تجديد عقده مع ليفربول',
          summary: 'مفاوضات متقدمة بين النجم المصري وإدارة النادي الإنجليزي',
          image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300&h=200&fit=crop',
          time: new Date(now.getTime() - 4 * 60 * 60 * 1000).toISOString(), // منذ 4 ساعات
          category: 'كرة القدم',
          isBreaking: false
        },
        {
          id: 3,
          title: 'الأهلي يستعد لمواجهة الزمالك في الدوري',
          summary: 'الفريق الأحمر يخوض تدريباته الأخيرة قبل ديربي القاهرة',
          image: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=300&h=200&fit=crop',
          time: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(), // منذ 6 ساعات
          category: 'كرة القدم',
          isBreaking: false
        },
        {
          id: 4,
          title: 'نوفاك ديوكوفيتش يتأهل لنصف نهائي أستراليا المفتوحة',
          summary: 'اللاعب الصربي يواصل مشواره نحو اللقب الـ24',
          image: 'https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=300&h=200&fit=crop',
          time: new Date(now.getTime() - 8 * 60 * 60 * 1000).toISOString(), // منذ 8 ساعات
          category: 'تنس',
          isBreaking: false
        },
        {
          id: 5,
          title: 'المنتخب السعودي يستعد لكأس آسيا',
          summary: 'الأخضر يخوض معسكراً تدريبياً مكثفاً قبل انطلاق البطولة',
          image: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=300&h=200&fit=crop',
          time: new Date(now.getTime() - 12 * 60 * 60 * 1000).toISOString(), // منذ 12 ساعة
          category: 'كرة القدم',
          isBreaking: false
        }
      ];
    };

    const mockNews = getCurrentNews();

    setTimeout(() => {
      setNews(mockNews);
      setLoading(false);
    }, 800);

    // تحديث الأخبار كل 5 دقائق
    const interval = setInterval(() => {
      setNews(getCurrentNews());
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (timeString) => {
    const date = new Date(timeString);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'منذ دقائق';
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`;
    } else {
      return `منذ ${Math.floor(diffInHours / 24)} يوم`;
    }
  };

  if (loading) {
    return (
      <div className="bg-dark-800 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4 arabic-text">
          الأخبار الرياضية
        </h3>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="flex gap-4">
                <div className="w-20 h-16 bg-dark-700 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-dark-700 rounded mb-2"></div>
                  <div className="h-3 bg-dark-700 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-bold text-white arabic-text">
              📰 الأخبار الرياضية
            </h3>
            <p className="text-sm text-gray-400 arabic-text">آخر التحديثات</p>
          </div>
        </div>
        <button className="btn btn-secondary text-xs">
          عرض الكل
        </button>
      </div>

      <div className="space-y-4">
        {news.map((article, index) => (
          <div
            key={article.id}
            className={`
              relative flex gap-4 p-4 rounded-xl transition-all duration-300 cursor-pointer group
              ${article.isBreaking
                ? 'bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30'
                : 'glass hover:bg-white/10'
              }
            `}
          >
            {/* شارة عاجل */}
            {article.isBreaking && (
              <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-lg font-bold animate-pulse">
                🔴 عاجل
              </div>
            )}

            <div className="flex-shrink-0">
              <div className="relative">
                <img
                  src={article.image}
                  alt={article.title}
                  className="w-24 h-20 object-cover rounded-xl group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    e.target.src = `https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=96&h=80&fit=crop`;
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-xl"></div>
                <div className="absolute bottom-1 left-1 text-white text-xs font-bold">
                  {index + 1}
                </div>
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <h4 className="text-white font-semibold text-sm mb-2 arabic-text line-clamp-2 group-hover:text-primary-400 transition-colors duration-200">
                {article.title}
              </h4>

              <p className="text-gray-300 text-xs mb-3 arabic-text line-clamp-2">
                {article.summary}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-primary-500/20 text-primary-400 text-xs rounded-lg font-medium arabic-text">
                    {article.category}
                  </span>
                  {article.isBreaking && (
                    <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-lg font-medium">
                      HOT
                    </span>
                  )}
                </div>
                <span className="text-gray-500 text-xs arabic-text">
                  {formatTime(article.time)}
                </span>
              </div>
            </div>

            {/* أيقونة القراءة */}
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-500/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <svg className="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* رابط لمزيد من الأخبار */}
      <div className="mt-6 pt-4 border-t border-white/10">
        <button className="w-full btn btn-secondary arabic-text">
          📱 تصفح جميع الأخبار الرياضية
        </button>
      </div>
    </div>
  );
};

export default SportsNews;
