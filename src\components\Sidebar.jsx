import React, { useState } from 'react';
import SportsNews from './SportsNews';
import UpcomingMatches from './UpcomingMatches';

const Sidebar = ({ onChannelSelect }) => {
  const [activeTab, setActiveTab] = useState('matches');

  const tabs = [
    {
      id: 'matches',
      name: 'المباريات',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      )
    },
    {
      id: 'news',
      name: 'الأخبار',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
        </svg>
      )
    }
  ];

  return (
    <div className="w-full lg:w-80 space-y-6">
      {/* تبويبات */}
      <div className="bg-dark-800 rounded-xl p-2">
        <div className="flex gap-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 flex-1 justify-center arabic-text
                ${activeTab === tab.id 
                  ? 'bg-primary-600 text-white shadow-lg' 
                  : 'text-gray-300 hover:bg-dark-700 hover:text-white'
                }
              `}
            >
              {tab.icon}
              <span className="font-medium">{tab.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* محتوى التبويبات */}
      <div className="space-y-6">
        {activeTab === 'matches' && (
          <UpcomingMatches onChannelSelect={onChannelSelect} />
        )}
        
        {activeTab === 'news' && (
          <SportsNews />
        )}
      </div>

      {/* إحصائيات سريعة */}
      <div className="bg-dark-800 rounded-xl p-6">
        <h3 className="text-lg font-bold text-white mb-4 arabic-text">
          إحصائيات سريعة
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm arabic-text">القنوات النشطة</span>
            <span className="text-primary-400 font-semibold">16</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm arabic-text">المباريات اليوم</span>
            <span className="text-green-500 font-semibold">8</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm arabic-text">المشاهدون الآن</span>
            <span className="text-yellow-500 font-semibold">1.2K</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm arabic-text">جودة البث</span>
            <span className="text-blue-500 font-semibold">HD</span>
          </div>
        </div>
      </div>

      {/* روابط سريعة */}
      <div className="bg-dark-800 rounded-xl p-6">
        <h3 className="text-lg font-bold text-white mb-4 arabic-text">
          روابط سريعة
        </h3>
        
        <div className="space-y-3">
          <button className="w-full text-left p-3 rounded-lg hover:bg-dark-700 transition-colors duration-200 group">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div>
                <p className="text-white text-sm font-medium arabic-text group-hover:text-primary-400">
                  الدوريات الأوروبية
                </p>
                <p className="text-gray-400 text-xs arabic-text">
                  مشاهدة أهم المباريات
                </p>
              </div>
            </div>
          </button>

          <button className="w-full text-left p-3 rounded-lg hover:bg-dark-700 transition-colors duration-200 group">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18 4l2 4h-3l-2-4h-2l2 4h-3l-2-4H8l2 4H7L5 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4h-4z"/>
                </svg>
              </div>
              <div>
                <p className="text-white text-sm font-medium arabic-text group-hover:text-primary-400">
                  الأفلام والمسلسلات
                </p>
                <p className="text-gray-400 text-xs arabic-text">
                  أحدث الإنتاجات
                </p>
              </div>
            </div>
          </button>

          <button className="w-full text-left p-3 rounded-lg hover:bg-dark-700 transition-colors duration-200 group">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                </svg>
              </div>
              <div>
                <p className="text-white text-sm font-medium arabic-text group-hover:text-primary-400">
                  البرامج الترفيهية
                </p>
                <p className="text-gray-400 text-xs arabic-text">
                  أشهر البرامج العربية
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* معلومات التطبيق */}
      <div className="bg-gradient-to-br from-primary-600 to-primary-800 rounded-xl p-6 text-center">
        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-3">
          <svg className="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        
        <h4 className="text-white font-bold mb-2 arabic-text">QLQ TV</h4>
        <p className="text-blue-100 text-sm mb-4 arabic-text">
          أفضل منصة للبث المباشر
        </p>
        
        <div className="flex items-center justify-center gap-4 text-blue-100 text-xs">
          <span className="arabic-text">الإصدار 1.0</span>
          <span>•</span>
          <span className="arabic-text">2024</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
