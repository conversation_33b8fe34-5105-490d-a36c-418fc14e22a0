@echo off
echo ========================================
echo       QLQ TV - Starting Application
echo ========================================
echo.

echo Installing dependencies...
call npm install

echo.
echo Installing server dependencies...
cd server
call npm install
cd ..

echo.
echo Starting QLQ TV...
echo Frontend: http://localhost:5173
echo Backend API: http://localhost:3001
echo.

start "QLQ TV Frontend" cmd /k "npm run dev"
timeout /t 3 /nobreak > nul
start "QLQ TV Backend" cmd /k "npm run server"

echo.
echo QLQ TV is starting...
echo Check the opened terminal windows for status.
echo.
pause
