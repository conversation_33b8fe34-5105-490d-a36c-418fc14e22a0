# QLQ TV Docker Configuration
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    bash \
    curl \
    git

# Copy package files
COPY package*.json ./
COPY server/package*.json ./server/

# Install dependencies
RUN npm install
RUN cd server && npm install

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose ports
EXPOSE 5173 3001

# Create startup script
RUN echo '#!/bin/bash' > /app/docker-start.sh && \
    echo 'cd /app/server && npm start &' >> /app/docker-start.sh && \
    echo 'cd /app && npm run preview' >> /app/docker-start.sh && \
    chmod +x /app/docker-start.sh

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5173 || exit 1

# Start the application
CMD ["/app/docker-start.sh"]
