{"name": "qlq-tv", "private": true, "version": "1.0.0", "type": "module", "description": "QLQ TV - Professional IPTV Streaming Platform for beIN Sports & OSN", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "server": "cd server && npm install && npm run dev", "server:prod": "cd server && npm install && npm start", "start": "npm run build && npm run preview", "full-dev": "concurrently \"npm run dev\" \"npm run server\"", "full-prod": "concurrently \"npm run preview\" \"npm run server:prod\"", "docker:build": "docker build -t qlq-tv .", "docker:run": "docker run -p 5173:5173 -p 3001:3001 qlq-tv", "docker:compose": "docker-compose up -d", "docker:down": "docker-compose down", "clean": "rm -rf node_modules dist server/node_modules", "setup": "npm install && cd server && npm install", "test": "echo \"Tests will be added soon\" && exit 0"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.7", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "cors": "^2.8.5", "express": "^5.1.0", "hls.js": "^1.6.2", "postcss": "^8.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.7", "video.js": "^8.22.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}