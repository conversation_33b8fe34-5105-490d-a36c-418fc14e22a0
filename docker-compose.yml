version: '3.8'

services:
  qlq-tv:
    build: .
    container_name: qlq-tv-app
    ports:
      - "5173:5173"
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=http://localhost:3001/api
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - qlq-network

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: qlq-tv-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - qlq-network

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: qlq-tv-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - qlq-tv
    restart: unless-stopped
    networks:
      - qlq-network

volumes:
  redis_data:

networks:
  qlq-network:
    driver: bridge
