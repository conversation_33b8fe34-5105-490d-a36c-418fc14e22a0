// خدمة البحث عن مصادر البث المباشر للقنوات
class StreamService {
  constructor() {
    // مصادر البث الحقيقية
    this.realStreamSources = {
      'bein1': [
        'https://d1211whpimeups.cloudfront.net/smil:bein01.smil/playlist.m3u8',
        'https://bein-1-hd.vercel.app/live.m3u8',
        'https://stream.bein.com/bein1/playlist.m3u8'
      ],
      'bein2': [
        'https://d1211whpimeups.cloudfront.net/smil:bein02.smil/playlist.m3u8',
        'https://bein-2-hd.vercel.app/live.m3u8'
      ],
      'osn1': [
        'https://osn-sports-1.vercel.app/live.m3u8',
        'https://stream.osn.com/sports1/playlist.m3u8'
      ]
    };

    // مصادر البحث عن القنوات
    this.searchAPIs = [
      'https://iptv-org.github.io/api/channels.json',
      'https://raw.githubusercontent.com/iptv-org/iptv/master/channels/ae.m3u',
      'https://raw.githubusercontent.com/iptv-org/iptv/master/channels/sa.m3u'
    ];

    // مصادر إضافية للبث المباشر
    this.backupSources = [
      'https://live-hls-web-aje.getaj.net/AJE/index.m3u8',
      'https://live-hls-web-ajb.getaj.net/AJB/index.m3u8',
      'https://dmi3qtvgjmn82.cloudfront.net/hls/main.m3u8'
    ];
  }

  // البحث عن مصادر البث للقناة
  async findStreamsForChannel(channelId, channelName) {
    try {
      const streams = [];

      // البحث في المصادر الحقيقية أولاً
      if (this.realStreamSources[channelId]) {
        const realStreams = this.realStreamSources[channelId].map((url, index) => ({
          url: url,
          quality: index === 0 ? 'FHD' : index === 1 ? 'HD' : 'SD',
          source: 'Official Stream',
          type: 'hls',
          status: 'active',
          priority: 1
        }));
        streams.push(...realStreams);
      }

      // البحث في مصادر إضافية
      const searchResults = await Promise.allSettled([
        this.searchInIPTVOrg(channelName),
        this.searchInPublicSources(channelName),
        this.searchInM3UPlaylists(channelName),
        this.searchInBackupSources(channelName)
      ]);

      searchResults.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          streams.push(...result.value);
        }
      });

      // إزالة المصادر المكررة
      const uniqueStreams = this.removeDuplicateStreams(streams);

      // ترتيب المصادر حسب الأولوية والجودة
      const sortedStreams = this.sortStreamsByPriority(uniqueStreams);

      // فحص جودة المصادر (فحص سريع للمصادر الأولى فقط)
      const validStreams = await this.validateStreams(sortedStreams.slice(0, 5));

      return validStreams.length > 0 ? validStreams : sortedStreams.slice(0, 3);
    } catch (error) {
      console.error('خطأ في البحث عن مصادر البث:', error);
      return this.getFallbackStreams(channelId);
    }
  }

  // البحث في IPTV-Org
  async searchInIPTVOrg(channelName) {
    try {
      // محاكاة البحث في IPTV-Org
      const mockStreams = [
        {
          url: `https://example-stream1.m3u8`,
          quality: 'HD',
          source: 'IPTV-Org',
          type: 'hls'
        },
        {
          url: `https://example-stream2.m3u8`,
          quality: 'SD',
          source: 'IPTV-Org',
          type: 'hls'
        }
      ];

      return mockStreams;
    } catch (error) {
      console.error('خطأ في البحث في IPTV-Org:', error);
      return [];
    }
  }

  // البحث في المصادر العامة
  async searchInPublicSources(channelName) {
    try {
      // محاكاة البحث في المصادر العامة
      const mockStreams = [
        {
          url: `https://public-stream1.m3u8`,
          quality: 'HD',
          source: 'Public',
          type: 'hls'
        }
      ];

      return mockStreams;
    } catch (error) {
      console.error('خطأ في البحث في المصادر العامة:', error);
      return [];
    }
  }

  // البحث في قوائم M3U
  async searchInM3UPlaylists(channelName) {
    try {
      // محاكاة البحث في قوائم M3U
      const mockStreams = [
        {
          url: `https://m3u-stream1.m3u8`,
          quality: 'FHD',
          source: 'M3U Playlist',
          type: 'hls',
          priority: 3
        }
      ];

      return mockStreams;
    } catch (error) {
      console.error('خطأ في البحث في قوائم M3U:', error);
      return [];
    }
  }

  // البحث في المصادر الاحتياطية
  async searchInBackupSources(channelName) {
    try {
      const backupStreams = this.backupSources.map((url, index) => ({
        url: url,
        quality: 'HD',
        source: 'Backup Stream',
        type: 'hls',
        priority: 4
      }));

      return backupStreams;
    } catch (error) {
      console.error('خطأ في البحث في المصادر الاحتياطية:', error);
      return [];
    }
  }

  // ترتيب المصادر حسب الأولوية
  sortStreamsByPriority(streams) {
    return streams.sort((a, b) => {
      // ترتيب حسب الأولوية أولاً
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // ثم حسب الجودة
      const qualityOrder = { 'FHD': 3, 'HD': 2, 'SD': 1 };
      const qualityA = qualityOrder[a.quality] || 0;
      const qualityB = qualityOrder[b.quality] || 0;
      return qualityB - qualityA;
    });
  }

  // الحصول على مصادر احتياطية
  getFallbackStreams(channelId) {
    return [
      {
        url: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
        quality: 'HD',
        source: 'Demo Stream',
        type: 'hls',
        status: 'active',
        priority: 5
      }
    ];
  }

  // إزالة المصادر المكررة
  removeDuplicateStreams(streams) {
    const seen = new Set();
    return streams.filter(stream => {
      const key = stream.url;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  // فحص صحة المصادر
  async validateStreams(streams) {
    const validStreams = [];

    for (const stream of streams) {
      try {
        // محاكاة فحص المصدر
        const isValid = await this.checkStreamHealth(stream.url);
        if (isValid) {
          validStreams.push({
            ...stream,
            status: 'active',
            lastChecked: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error(`خطأ في فحص المصدر ${stream.url}:`, error);
      }
    }

    return validStreams;
  }

  // فحص صحة المصدر
  async checkStreamHealth(streamUrl) {
    try {
      // محاكاة فحص صحة المصدر
      // في التطبيق الحقيقي، يمكن إرسال طلب HEAD للتحقق من المصدر
      return Math.random() > 0.3; // 70% احتمال أن يكون المصدر صالح
    } catch (error) {
      return false;
    }
  }

  // الحصول على أفضل مصدر للقناة
  getBestStream(streams) {
    if (!streams || streams.length === 0) {
      return null;
    }

    // ترتيب المصادر حسب الجودة
    const qualityOrder = { 'FHD': 3, 'HD': 2, 'SD': 1 };

    return streams.sort((a, b) => {
      const qualityA = qualityOrder[a.quality] || 0;
      const qualityB = qualityOrder[b.quality] || 0;
      return qualityB - qualityA;
    })[0];
  }

  // تحديث مصادر القناة
  async updateChannelStreams(channel) {
    const streams = await this.findStreamsForChannel(channel.id, channel.name);
    return {
      ...channel,
      streams: streams,
      lastUpdated: new Date().toISOString()
    };
  }

  // البحث عن قنوات جديدة
  async searchNewChannels(query) {
    try {
      // محاكاة البحث عن قنوات جديدة
      const mockChannels = [
        {
          id: `search_${Date.now()}`,
          name: `نتيجة البحث: ${query}`,
          logo: 'https://via.placeholder.com/150',
          category: 'search',
          language: 'ar',
          description: `قناة تم العثور عليها من البحث: ${query}`,
          streams: []
        }
      ];

      return mockChannels;
    } catch (error) {
      console.error('خطأ في البحث عن قنوات جديدة:', error);
      return [];
    }
  }
}

export default new StreamService();
