import React, { useState, useEffect } from 'react';

const UpcomingMatches = ({ onChannelSelect }) => {
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // تحميل المباريات القادمة والحالية
    const getCurrentMatches = () => {
      const now = new Date();

      return [
        {
          id: 1,
          homeTeam: 'مانشستر سيتي',
          awayTeam: 'ريال مدريد',
          homeTeamLogo: 'https://logos-world.net/wp-content/uploads/2020/06/Manchester-City-Logo.png',
          awayTeamLogo: 'https://logos-world.net/wp-content/uploads/2020/06/Real-Madrid-Logo.png',
          date: new Date(now.getTime() + 2 * 60 * 60 * 1000).toISOString(), // خلال ساعتين
          competition: 'دوري أبطال أوروبا',
          channel: 'beIN Sports 1 HD',
          channelId: 'bein1',
          status: 'upcoming',
          importance: 'high'
        },
        {
          id: 2,
          homeTeam: 'ليفربول',
          awayTeam: 'أرسنال',
          homeTeamLogo: 'https://logos-world.net/wp-content/uploads/2020/06/Liverpool-Logo.png',
          awayTeamLogo: 'https://logos-world.net/wp-content/uploads/2020/06/Arsenal-Logo.png',
          date: new Date(now.getTime() + 4 * 60 * 60 * 1000).toISOString(), // خلال 4 ساعات
          competition: 'الدوري الإنجليزي الممتاز',
          channel: 'beIN Sports 2 HD',
          channelId: 'bein2',
          status: 'upcoming',
          importance: 'medium'
        },
        {
          id: 3,
          homeTeam: 'الأهلي',
          awayTeam: 'الزمالك',
          homeTeamLogo: 'https://upload.wikimedia.org/wikipedia/en/4/4a/Al_Ahly_SC_logo.png',
          awayTeamLogo: 'https://upload.wikimedia.org/wikipedia/en/e/ec/Zamalek_SC_logo.svg',
          date: new Date(now.getTime() - 30 * 60 * 1000).toISOString(), // بدأت منذ 30 دقيقة
          competition: 'الدوري المصري الممتاز',
          channel: 'beIN Sports 3 HD',
          channelId: 'bein3',
          status: 'live',
          score: '1-0',
          minute: 67,
          importance: 'high'
        },
        {
          id: 4,
          homeTeam: 'باريس سان جيرمان',
          awayTeam: 'مارسيليا',
          homeTeamLogo: 'https://logos-world.net/wp-content/uploads/2020/06/Paris-Saint-Germain-Logo.png',
          awayTeamLogo: 'https://logos-world.net/wp-content/uploads/2020/06/Olympique-Marseille-Logo.png',
          date: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString(), // غداً
          competition: 'الدوري الفرنسي',
          channel: 'beIN Sports 4 HD',
          channelId: 'bein4',
          status: 'upcoming',
          importance: 'medium'
        },
        {
          id: 5,
          homeTeam: 'الهلال',
          awayTeam: 'النصر',
          homeTeamLogo: 'https://upload.wikimedia.org/wikipedia/en/b/be/Al-Hilal_FC_Logo.png',
          awayTeamLogo: 'https://upload.wikimedia.org/wikipedia/en/c/c4/Al_Nassr_FC_Logo.png',
          date: new Date(now.getTime() + 6 * 60 * 60 * 1000).toISOString(), // خلال 6 ساعات
          competition: 'دوري روشن السعودي',
          channel: 'SSC 1 HD',
          channelId: 'ssc1',
          status: 'upcoming',
          importance: 'high'
        }
      ];
    };

    const mockMatches = getCurrentMatches();

    setTimeout(() => {
      setMatches(mockMatches);
      setLoading(false);
    }, 600);

    // تحديث المباريات كل دقيقة
    const interval = setInterval(() => {
      setMatches(getCurrentMatches());
    }, 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const formatMatchTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((date - now) / (1000 * 60));

    if (diffInMinutes < 0) {
      return 'مباشر الآن';
    } else if (diffInMinutes < 60) {
      return `خلال ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) {
      return `خلال ${Math.floor(diffInMinutes / 60)} ساعة`;
    } else {
      return date.toLocaleDateString('ar-EG', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const getStatusBadge = (match) => {
    switch (match.status) {
      case 'live':
        return (
          <div className="flex items-center gap-2">
            <span className="bg-red-500 text-white text-xs px-3 py-1 rounded-full animate-pulse arabic-text font-bold">
              🔴 مباشر
            </span>
            {match.minute && (
              <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-lg font-bold">
                {match.minute}'
              </span>
            )}
          </div>
        );
      case 'upcoming':
        return (
          <div className="flex items-center gap-2">
            <span className={`text-white text-xs px-3 py-1 rounded-full arabic-text font-medium ${
              match.importance === 'high'
                ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                : 'bg-primary-600'
            }`}>
              {match.importance === 'high' ? '⭐ مهم' : '📅 قادم'}
            </span>
          </div>
        );
      default:
        return null;
    }
  };

  const handleWatchMatch = (match) => {
    // البحث عن القناة المطلوبة
    const channel = {
      id: match.channelId,
      name: match.channel,
      logo: 'https://i.imgur.com/8s6aK4t.png',
      category: 'sports',
      language: 'ar',
      description: `مشاهدة مباراة ${match.homeTeam} ضد ${match.awayTeam}`,
      streams: []
    };

    if (onChannelSelect) {
      onChannelSelect(channel);
    }
  };

  if (loading) {
    return (
      <div className="bg-dark-800 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4 arabic-text">
          المباريات القادمة
        </h3>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center justify-between p-4 bg-dark-700 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-dark-600 rounded-full"></div>
                  <div className="h-4 bg-dark-600 rounded w-20"></div>
                  <div className="w-8 h-8 bg-dark-600 rounded-full"></div>
                </div>
                <div className="h-3 bg-dark-600 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-bold text-white arabic-text">
              ⚽ المباريات القادمة
            </h3>
            <p className="text-sm text-gray-400 arabic-text">{matches.length} مباراة اليوم</p>
          </div>
        </div>
        <button className="btn btn-secondary text-xs">
          عرض الكل
        </button>
      </div>

      <div className="space-y-4">
        {matches.map((match, index) => (
          <div
            key={match.id}
            className={`
              relative glass rounded-xl p-4 cursor-pointer group transition-all duration-300
              ${match.status === 'live'
                ? 'bg-gradient-to-r from-red-500/20 to-red-600/20 border border-red-500/30'
                : 'hover:bg-white/10'
              }
            `}
            onClick={() => handleWatchMatch(match)}
          >
            {/* رقم المباراة */}
            <div className="absolute -top-2 -left-2 w-6 h-6 bg-primary-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
              {index + 1}
            </div>

            {/* معلومات المباراة العلوية */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <span className="text-xs text-gray-300 arabic-text bg-dark-700/50 px-2 py-1 rounded-lg">
                  {match.competition}
                </span>
                {getStatusBadge(match)}
              </div>
              <span className="text-xs text-primary-400 arabic-text font-medium">
                📺 {match.channel}
              </span>
            </div>

            {/* الفرق والنتيجة */}
            <div className="flex items-center justify-between mb-4">
              {/* الفريق المضيف */}
              <div className="flex items-center gap-3 flex-1">
                <div className="relative">
                  <img
                    src={match.homeTeamLogo}
                    alt={match.homeTeam}
                    className="w-12 h-12 rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-200"
                    onError={(e) => {
                      e.target.src = 'https://via.placeholder.com/48x48/2563eb/ffffff?text=' + encodeURIComponent(match.homeTeam.substring(0, 2));
                    }}
                  />
                </div>
                <div className="flex-1">
                  <span className="text-white text-sm font-semibold arabic-text block">
                    {match.homeTeam}
                  </span>
                  <span className="text-gray-400 text-xs arabic-text">المضيف</span>
                </div>
              </div>

              {/* النتيجة أو VS */}
              <div className="flex flex-col items-center px-4">
                {match.status === 'live' && match.score ? (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">
                      {match.score}
                    </div>
                    <div className="text-xs text-red-400 font-medium">
                      الدقيقة {match.minute}
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-400 mb-1">VS</div>
                    <div className="text-xs text-gray-500">
                      {formatMatchTime(match.date)}
                    </div>
                  </div>
                )}
              </div>

              {/* الفريق الضيف */}
              <div className="flex items-center gap-3 flex-1 justify-end">
                <div className="flex-1 text-right">
                  <span className="text-white text-sm font-semibold arabic-text block">
                    {match.awayTeam}
                  </span>
                  <span className="text-gray-400 text-xs arabic-text">الضيف</span>
                </div>
                <div className="relative">
                  <img
                    src={match.awayTeamLogo}
                    alt={match.awayTeam}
                    className="w-12 h-12 rounded-xl shadow-lg group-hover:scale-105 transition-transform duration-200"
                    onError={(e) => {
                      e.target.src = 'https://via.placeholder.com/48x48/2563eb/ffffff?text=' + encodeURIComponent(match.awayTeam.substring(0, 2));
                    }}
                  />
                </div>
              </div>
            </div>

            {/* زر المشاهدة */}
            <div className="flex items-center justify-between pt-3 border-t border-white/10">
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <span className="arabic-text">
                  {match.importance === 'high' ? 'مباراة مهمة' : 'مباراة عادية'}
                </span>
              </div>

              <button className="btn btn-primary text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                {match.status === 'live' ? '🔴 مشاهدة مباشرة' : '📅 تذكيرني'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* رابط لمزيد من المباريات */}
      <div className="mt-6 pt-4 border-t border-white/10">
        <button className="w-full btn btn-secondary arabic-text">
          📅 عرض جميع المباريات هذا الأسبوع
        </button>
      </div>
    </div>
  );
};

export default UpcomingMatches;
